# Google Analytics Setup with vue-gtag

This document explains how Google Analytics is integrated into the AgentQ frontend application using vue-gtag.

## Overview

The application uses `vue-gtag` v3.6.1 to integrate Google Analytics 4 (GA4) for tracking user interactions, page views, and custom events.

## Configuration

### Environment Variables

The Google Analytics tracking ID is configured through environment variables:

- **Development**: `.env` file
- **Production**: `.env.production` file

```bash
# Google Analytics
VITE_GA_TRACKING_ID=G-JVSJ3Q22KF
```

### Main Configuration

The vue-gtag is configured in `src/main.ts`:

```typescript
import { configure } from 'vue-gtag';

// Configure Google Analytics with vue-gtag
configure({
  tagId: (import.meta as any).env.VITE_GA_TRACKING_ID || 'GA_MEASUREMENT_ID',
  config: {
    page_title: document.title,
    page_location: window.location.href,
  }
});
```

### Router Integration

Page view tracking is automatically handled through Vue Router in `src/router/index.ts`:

```typescript
import { pageview } from 'vue-gtag'

// Google Analytics page tracking
router.afterEach((to) => {
  // Track page view with Google Analytics
  if ((import.meta as any).env.VITE_GA_TRACKING_ID) {
    pageview({
      page_title: to.meta.title as string || document.title,
      page_location: window.location.href,
      page_path: to.fullPath
    })
  }
})
```

## Analytics Utilities

A comprehensive analytics utility file is available at `src/utils/analytics.ts` with the following functions:

### Basic Event Tracking

```typescript
import { trackEvent } from '@/utils/analytics'

// Track any custom event
trackEvent('custom_event_name', {
  custom_parameter: 'value',
  another_parameter: 123
})
```

### Authentication Events

```typescript
import { trackLogin, trackSignup } from '@/utils/analytics'

// Track user login
trackLogin('email')        // For email login
trackLogin('github')       // For GitHub login
trackLogin('google')       // For Google login

// Track user signup
trackSignup('email')       // For email signup
trackSignup('github')      // For GitHub signup
```

### UI Interaction Events

```typescript
import { trackButtonClick } from '@/utils/analytics'

// Track button clicks
trackButtonClick('submit_button', 'contact_form')
trackButtonClick('download_button', 'pricing_page')
```

### Form Events

```typescript
import { trackFormSubmission } from '@/utils/analytics'

// Track form submissions
trackFormSubmission('contact_form', true)   // Successful submission
trackFormSubmission('login_form', false)    // Failed submission
```

### Error Tracking

```typescript
import { trackError } from '@/utils/analytics'

// Track errors
trackError('API request failed', 'network_error')
trackError('Validation failed', 'form_error')
```

### Engagement Tracking

```typescript
import { trackEngagement } from '@/utils/analytics'

// Track time spent on page (in milliseconds)
trackEngagement(30000, 'dashboard')  // 30 seconds on dashboard
```

## Usage Examples

### In Vue Components

```vue
<template>
  <button @click="handleSubmit">Submit Form</button>
</template>

<script setup lang="ts">
import { trackButtonClick, trackFormSubmission, trackError } from '@/utils/analytics'

const handleSubmit = async () => {
  try {
    // Track button click
    trackButtonClick('submit_form', 'contact_page')
    
    // Your form submission logic here
    const result = await submitForm()
    
    if (result.success) {
      // Track successful submission
      trackFormSubmission('contact_form', true)
    } else {
      // Track failed submission
      trackFormSubmission('contact_form', false)
    }
  } catch (error) {
    // Track error
    trackError(error.message, 'form_submission')
  }
}
</script>
```

### Page-specific Tracking

```vue
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { trackEngagement } from '@/utils/analytics'

let startTime: number

onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  const timeSpent = Date.now() - startTime
  trackEngagement(timeSpent, 'dashboard')
})
</script>
```

## Event Categories

The analytics utilities use the following event categories:

- `authentication` - Login, signup, logout events
- `ui_interaction` - Button clicks, menu interactions
- `form_interaction` - Form submissions, validations
- `engagement` - Time on page, scroll depth
- `error` - Error tracking and debugging
- `navigation` - Page views, route changes

## Best Practices

1. **Consistent Naming**: Use consistent naming conventions for events and parameters
2. **Meaningful Parameters**: Include relevant context in event parameters
3. **Error Tracking**: Always track errors for debugging and improvement
4. **User Privacy**: Ensure no personally identifiable information (PII) is tracked
5. **Performance**: Analytics tracking should not impact application performance

## Testing

To test the Google Analytics integration:

1. Open browser developer tools
2. Go to Network tab
3. Filter by "google-analytics" or "gtag"
4. Navigate through the application and trigger events
5. Verify that analytics requests are being sent

## Debugging

To debug analytics in development:

1. Install the Google Analytics Debugger browser extension
2. Enable debug mode in the browser console:
   ```javascript
   window.gtag('config', 'GA_MEASUREMENT_ID', { debug_mode: true })
   ```
3. Check the browser console for debug messages

## Production Deployment

Ensure the production environment variables are properly set:

```bash
# .env.production
VITE_GA_TRACKING_ID=G-YOUR-PRODUCTION-ID
```

The tracking will only be active when the `VITE_GA_TRACKING_ID` environment variable is set.
