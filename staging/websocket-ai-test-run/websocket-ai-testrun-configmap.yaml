apiVersion: v1
kind: ConfigMap
metadata:
  name: websocket-ai-testrun-config
  namespace: default
data:
  BACKEND_URL:              "https://staging-backend-app.agentq.id"
  VITE_BACKEND_URL:         "https://staging-backend-app.agentq.id"
  AGENTQ_API_URL:           "https://staging-backend-app.agentq.id"
  VITE_AI_SERVICE_URL:      "https://staging-ai-service.agentq.id"
  PORT:                     "3022"
  LLM_PROVIDER:             "GEMINI"
  GEMINI_MODEL:             "gemini-1.5-flash"
  OPENAI_MODEL:             "gpt-4o-mini"
  CORE_SERVICE_URL:         "https://staging-backend-core-api.agentq.id"
  GCP_PROJECT_ID:           "orbital-nirvana-447600-j8"
  GCP_CLIENT_EMAIL:         "<EMAIL>"
  GCP_BUCKET_NAME:          "agentq"
  ENABLE_CLOUD_STORAGE:     "true"
  NODE_ENV:                 "staging"
  VITE_WEBSOCKET_URL:       "wss://staging-websocket-ai-test-run.agentq.id"
  REDIS_HOST:               "***********"          
  REDIS_PORT:               "6379"
  REDIS_PASSWORD:           ""
  REDIS_DB:                 "3"
