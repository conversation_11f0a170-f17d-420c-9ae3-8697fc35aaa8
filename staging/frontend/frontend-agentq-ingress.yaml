apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-agentq-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
    - hosts:
        - staging.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: staging.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: frontend-agentq-svc
              port:
                number: 80
