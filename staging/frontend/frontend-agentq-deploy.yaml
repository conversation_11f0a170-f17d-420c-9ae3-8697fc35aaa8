apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-agentq
  namespace: default
  labels:
    app: frontend-agentq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-agentq
  template:
    metadata:
      labels:
        app: frontend-agentq
    spec:
      containers:
      - name: frontend-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/frontend_agentq:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: frontend-agentq-config
#        volumeMounts:
#        - name: env-js
#          mountPath: /usr/share/nginx/html/env.js
#          subPath: env.js
#      volumes:
#      - name: env-js
#        configMap:
#          name: frontend-env-js
