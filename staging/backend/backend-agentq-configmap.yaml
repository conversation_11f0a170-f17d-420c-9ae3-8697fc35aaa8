apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-agentq-config
  namespace: default
data:
  DB_HOST: "***********"
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_DATABASE: "agentq"
  REDIS_URL: "redis://***********:6379"
  REDIS_PASSWORD: ""
  REDIS_DB: "0"
  JWT_SECRET: "your_jwt_secret_key_change_in_production"
  GITHUB_CLIENT_ID: "********************"
  GITHUB_CLIENT_SECRET: "8b5b9f61b5cce7070d4b077f15106ae1b90fb6ac"
  GITHUB_CALLBACK_URL: "http://localhost:3000/auth/github/callback"
  GOOGLE_CLIENT_ID: "1057703877528-ieclfups0msr8nkuuqp17j5f0snm9rcv.apps.googleusercontent.com"
  GOOGLE_CLIENT_SECRET: "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"
  GOOGLE_CALLBACK_URL: "http://localhost:3000/auth/google/callback"
  FRONTEND_URL: "http://localhost:5173"
  AGENTQ_APP_URL: "http://localhost:5174"
  AGENTQ_APP_API_URL: "http://localhost:3010"
  RESEND_API_KEY: "re_LjAfU3GD_FWKthbMiSaHKmR1jXXZFqZJk"
  RESEND_FROM_EMAIL: "<EMAIL>"
  RESEND_FROM_NAME: "AgentQ"
  MIDTRANS_CLIENT_KEY: "SB-Mid-client-o-6cg2r_xqlG7jaI"
  MIDTRANS_SERVER_KEY: "SB-Mid-server-xTnMl3ofd5TTsaqPvdKjlPGi"
