apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-agentq
  namespace: default
  labels:
    app: backend-agentq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend-agentq
  template:
    metadata:
      labels:
        app: backend-agentq
    spec:
      containers:
      - name: backend-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_agentq:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: backend-agentq-config
        - secretRef:
            name: backend-agentq-secrets
