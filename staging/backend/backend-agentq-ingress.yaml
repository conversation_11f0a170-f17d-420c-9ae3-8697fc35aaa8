apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-agentq-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
    - host: staging-backend-core-api.agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backend-agentq-svc
                port:
                  number: 80
