apiVersion: v1
kind: Secret
metadata:
  name: backend-agentq-secrets
  namespace: default
type: Opaque
stringData:
  DB_PASSWORD: 'azq"D0sVt`<l{PkE'
  REDIS_PASSWORD: ""
  JWT_SECRET: "your_jwt_secret_key_change_in_production"
  GITHUB_CLIENT_SECRET: "8b5b9f61b5cce7070d4b077f15106ae1b90fb6ac"
  GOOGLE_CLIENT_SECRET: "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"
  RESEND_API_KEY: "re_LjAfU3GD_FWKthbMiSaHKmR1jXXZFqZJk"
  MIDTRANS_CLIENT_KEY: "SB-Mid-client-o-6cg2r_xqlG7jaI"
  MIDTRANS_SERVER_KEY: "SB-Mid-server-xTnMl3ofd5TTsaqPvdKjlPGi"
