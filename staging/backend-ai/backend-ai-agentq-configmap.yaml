apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-ai-agentq-config
  namespace: default
data:
  DB_HOST: "***********"          
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_DATABASE: "ai_service"
  GOOGLE_CLOUD_PROJECT_ID: "orbital-nirvana-447600-j8"
  GOOGLE_CLOUD_CLIENT_EMAIL: "<EMAIL>"
  GOOGLE_CLOUD_BUCKET: "agentq"
  LLM: "GEMINI"
  REDIS_HOST: "***********"
  REDIS_PORT: "6379"
  REDIS_DB: "1"
  NODE_ENV: "staging"