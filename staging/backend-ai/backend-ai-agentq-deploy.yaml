apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-ai-agentq
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend-ai-agentq
  template:
    metadata:
      labels:
        app: backend-ai-agentq
    spec:
      containers:
      - name: backend-ai-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_ai_agentq:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
        env:
        - name: PORT
          value: "3001"
        envFrom:
        - configMapRef:
            name: backend-ai-agentq-config
        - secretRef:
            name: backend-ai-agentq-secret
