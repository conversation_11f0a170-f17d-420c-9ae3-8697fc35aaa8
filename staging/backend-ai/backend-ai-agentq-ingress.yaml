apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-ai-agentq-ingress
  namespace: default
  annotations:
#    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
    - hosts:
        - staging-backend-ai-api.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: staging-backend-ai-api.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: backend-ai-agentq-svc
              port:
                number: 80
