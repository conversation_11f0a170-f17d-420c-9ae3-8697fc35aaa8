apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-ai-single-test
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: websocket-ai-single-test
  template:
    metadata:
      labels:
        app: websocket-ai-single-test
    spec:
      containers:
      - name: websocket-ai-single-test
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_single_test:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3021
        envFrom:
        - configMapRef:
            name: websocket-ai-single-test-config
        - secretRef:
            name: websocket-ai-single-test-secret