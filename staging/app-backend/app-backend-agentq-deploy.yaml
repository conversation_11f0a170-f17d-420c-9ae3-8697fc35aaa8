apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-backend-agentq
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app-backend-agentq
  template:
    metadata:
      labels:
        app: app-backend-agentq
    spec:
      containers:
      - name: app-backend-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_backend_agentq:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3010
        env:
        - name: PORT
          value: "3010"
        envFrom:
        - configMapRef:
            name: app-backend-agentq-config
        - secretRef:
            name: app-backend-agentq-secret
