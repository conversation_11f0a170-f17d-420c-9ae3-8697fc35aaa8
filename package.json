{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "chart.js": "^4.4.0", "pinia": "^2.1.7", "sass": "^1.86.3", "vue": "^3.5.13", "vue-gtag": "^3.6.1", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}