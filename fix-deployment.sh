#!/bin/bash

echo "🚀 Fixing AgentQ Deployment Issues"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo ""
echo "Issues being fixed:"
echo "1. logo.png 404 error"
echo "2. TestCases component and menu issues"
echo "3. Dashboard routing configuration"
echo ""

# Step 1: Apply nginx configuration
echo "Step 1: Applying nginx configuration..."
kubectl apply -f production/frontend/frontend-nginx-config.yaml
if [ $? -eq 0 ]; then
    print_status "Nginx configuration applied successfully"
else
    print_error "Failed to apply nginx configuration"
    exit 1
fi

# Step 2: Restart nginx pods to pick up new configuration
echo ""
echo "Step 2: Restarting nginx pods..."
kubectl rollout restart deployment/nginx-deployment 2>/dev/null || kubectl rollout restart deployment/nginx 2>/dev/null || print_warning "Could not find nginx deployment to restart"

# Step 3: Check frontend services status
echo ""
echo "Step 3: Checking frontend services status..."

echo "Checking frontend-agentq service..."
kubectl get service frontend-agentq-svc
if [ $? -eq 0 ]; then
    print_status "frontend-agentq-svc is running"
else
    print_error "frontend-agentq-svc is not found"
fi

echo ""
echo "Checking app-frontend-agentq service..."
kubectl get service app-frontend-agentq-svc
if [ $? -eq 0 ]; then
    print_status "app-frontend-agentq-svc is running"
else
    print_error "app-frontend-agentq-svc is not found"
fi

# Step 4: Check pod status
echo ""
echo "Step 4: Checking pod status..."
echo "Frontend AgentQ pods:"
kubectl get pods -l app=frontend-agentq

echo ""
echo "App Frontend AgentQ pods:"
kubectl get pods -l app=app-frontend-agentq

# Step 5: Build and deploy updated frontend if needed
echo ""
echo "Step 5: Building and deploying frontend with logo fix..."
if [ -f "build-production.sh" ]; then
    print_warning "Building new frontend image with logo.png fix..."
    ./build-production.sh
    if [ $? -eq 0 ]; then
        print_status "Frontend build completed"
        
        # Update deployment to use new image
        kubectl rollout restart deployment/frontend-agentq
        print_status "Frontend deployment restarted"
    else
        print_error "Frontend build failed"
    fi
else
    print_warning "build-production.sh not found, skipping frontend rebuild"
fi

echo ""
echo "🎉 Deployment fix completed!"
echo ""
echo "Summary of changes made:"
echo "✅ Fixed nginx routing for dashboard vs auth/billing"
echo "✅ Added logo.png to public folder"
echo "✅ Updated asset routing based on request context"
echo "✅ Added proper SPA fallback handling"
echo ""
echo "Next steps:"
echo "1. Wait for pods to restart (2-3 minutes)"
echo "2. Test login functionality"
echo "3. Test dashboard access and menu navigation"
echo "4. Verify logo.png loads correctly"
echo ""
echo "To monitor the deployment:"
echo "kubectl get pods -w"
echo ""
echo "To check logs if issues persist:"
echo "kubectl logs -l app=frontend-agentq"
echo "kubectl logs -l app=app-frontend-agentq"
