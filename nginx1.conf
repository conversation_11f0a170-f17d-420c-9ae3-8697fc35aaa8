server {
    listen 80;
    server_name agentq.id www.agentq.id;
    root /var/www/frontend_agentq/dist;
    index index.html;

    # Include MIME types
    include /etc/nginx/mime.types;

    # Redirect /signin to /login
    location = /signin {
        return 301 /login;
    }

    # Explicitly set MIME type for JavaScript files
    location ~* \.js$ {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Serve static assets locally
    location /assets/ {
        gzip_static on;
        expires max;
        add_header Cache-Control public;
    }

    # Serve other static assets (excluding JS which is handled above)
    location ~* \.(css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Handle Vue routes
    location ~ ^/(dashboard|login|signup|auth/callback|confirm-email|forgot-password|reset-password|confirm-email-member-invitation|payment/finish|payment/resume|invoice|settings) {
        try_files $uri $uri/ /index.html;
    }

    # Redirect root to /agentq
    location = / {
        return 301 /agentq/;
    }

    # Proxy /agentq to GitHub Pages
    location /agentq/ {
        proxy_pass https://agentq-ai.github.io/agentq/;
        proxy_set_header Host agentq-ai.github.io;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Fix asset paths
        sub_filter_once off;
        sub_filter_types text/html text/css application/javascript;

        # Fix for absolute URLs in HTML/CSS/JS
        sub_filter '"/' '"/agentq/';
        sub_filter "url(/" "url(/agentq/";
        sub_filter "src=/" "src=/agentq/";
        sub_filter "href=/" "href=/agentq/";

        # Don't modify URLs that are already correct
        sub_filter '"/agentq/' '"/agentq/';

        # Important for redirecting properly
        proxy_redirect https://agentq-ai.github.io/agentq/ /agentq/;
    }

    # Handle static asset requests in the agentq path
    location ~* ^/agentq/(.+\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))$ {
        proxy_pass https://agentq-ai.github.io/agentq/$1;
        proxy_set_header Host agentq-ai.github.io;
        expires max;
        add_header Cache-Control public;
    }

    # Optional: CORS headers
    add_header Access-Control-Allow-Origin *;
}
