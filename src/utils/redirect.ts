/**
 * Utility functions for handling redirects to the app frontend
 */

export const getAppUrl = (): string => {
  // For production, use the same domain with /dashboard path
  const currentOrigin = window.location.origin;
  const dashboardUrl = `${currentOrigin}/dashboard`;

  console.log('=== DEBUG: getAppUrl ===');
  console.log('Current origin:', currentOrigin);
  console.log('Dashboard URL:', dashboardUrl);

  return dashboardUrl;
};

export const redirectToApp = (delay: number = 100): void => {
  const appUrl = getAppUrl();
  console.log('Redirecting to app URL:', appUrl);

  setTimeout(() => {
    window.location.href = appUrl;
  }, delay);
};

export const redirectToAppWithPath = (path: string = '', delay: number = 100): void => {
  const appUrl = getAppUrl();
  const fullUrl = path ? `${appUrl}${path}` : appUrl;
  console.log('Redirecting to app URL with path:', fullUrl);

  setTimeout(() => {
    window.location.href = fullUrl;
  }, delay);
};

/**
 * Redirect to dashboard with authentication data
 * Since we're on the same domain, we can use localStorage directly
 */
export const redirectToAppWithAuth = (delay: number = 100): void => {
  const dashboardUrl = getAppUrl();

  // Get token and user from localStorage
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');

  if (!token || !user) {
    console.error('No token or user data found in localStorage');
    // Fallback to regular redirect
    redirectToApp(delay);
    return;
  }

  console.log('Redirecting to dashboard with auth:', dashboardUrl);

  setTimeout(() => {
    window.location.href = dashboardUrl;
  }, delay);
};

