import { event } from 'vue-gtag'

/**
 * Track custom events with Google Analytics
 */
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  // Only track if Google Analytics is configured
  if ((import.meta as any).env.VITE_GA_TRACKING_ID) {
    event(eventName, {
      event_category: 'engagement',
      event_label: eventName,
      ...parameters
    })
  }
}

/**
 * Track user login events
 */
export const trackLogin = (method: string = 'email') => {
  trackEvent('login', {
    method: method,
    event_category: 'authentication'
  })
}

/**
 * Track user signup events
 */
export const trackSignup = (method: string = 'email') => {
  trackEvent('sign_up', {
    method: method,
    event_category: 'authentication'
  })
}

/**
 * Track button clicks
 */
export const trackButtonClick = (buttonName: string, location?: string) => {
  trackEvent('button_click', {
    button_name: buttonName,
    location: location,
    event_category: 'ui_interaction'
  })
}

/**
 * Track form submissions
 */
export const trackFormSubmission = (formName: string, success: boolean = true) => {
  trackEvent('form_submit', {
    form_name: formName,
    success: success,
    event_category: 'form_interaction'
  })
}

/**
 * Track page engagement time
 */
export const trackEngagement = (timeOnPage: number, pageName?: string) => {
  trackEvent('engagement_time', {
    engagement_time_msec: timeOnPage,
    page_name: pageName,
    event_category: 'engagement'
  })
}

/**
 * Track errors
 */
export const trackError = (errorMessage: string, errorType?: string) => {
  trackEvent('exception', {
    description: errorMessage,
    error_type: errorType,
    fatal: false,
    event_category: 'error'
  })
}
