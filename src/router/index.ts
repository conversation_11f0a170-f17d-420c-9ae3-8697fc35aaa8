import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import SignupView from '../views/SignupView.vue'
import AuthCallback from '../views/AuthCallback.vue'
import HomeView from '../views/HomeView.vue'
import ConfirmationEmailView from '../views/ConfirmationEmailView.vue'
import ConfirmEmailMemberInvitationView from '../views/ConfirmEmailMemberInvitationView.vue'
import ForgotPasswordView from '../views/ForgotPasswordView.vue'
import ResetPasswordView from '../views/ResetPasswordView.vue'
import ConfigurationView from '../views/ConfigurationView.vue'
import { useAuthStore } from '../stores/auth'
import { pageview } from 'vue-gtag'

const router = createRouter({
  history: createWebHistory((import.meta as any).env.BASE_URL),
  routes: [
    {
      path: '/billing',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/signup',
      name: 'signup',
      component: SignupView
    },
    {
      path: '/auth/callback',
      name: 'auth-callback',
      component: AuthCallback
    },
    {
      path: '/confirm-email',
      name: 'confirm-email',
      component: ConfirmationEmailView
    },
    {
      path: '/confirm-email-member-invitation',
      name: 'confirm-email-member-invitation',
      component: ConfirmEmailMemberInvitationView
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: ForgotPasswordView
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: ResetPasswordView
    },
    {
      path: '/payment/finish',
      name: 'PaymentFinish',
      component: () => import('../views/PaymentFinishView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/payment/resume',
      name: 'PaymentResume',
      component: () => import('../views/PaymentResumeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/invoice/:id',
      name: 'InvoiceDetails',
      component: () => import('../views/InvoiceDetailsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/configuration',
      name: 'configuration',
      component: ConfigurationView,
      meta: { requiresAuth: true }
    }
  ]
})

// Navigation guard
router.beforeEach((to, _,next) => {
  const authStore = useAuthStore()

  // Check if token is in URL parameters (from app_frontend_agentq redirect)
  const params = new URLSearchParams(window.location.search);
  const tokenFromUrl = params.get('token');
  const userFromUrl = params.get('user');

  // If token is in URL and not in store, set it
  if (tokenFromUrl && !authStore.token) {
    authStore.setToken(tokenFromUrl, '', 3600);
    if (userFromUrl) {
      try {
        const user = JSON.parse(userFromUrl);
        authStore.setUser(user);
      } catch (e) {
        console.error('Failed to parse user from URL:', e);
      }
    }
    // Remove parameters from URL to clean it up
    window.history.replaceState({}, document.title, window.location.pathname);
  }

  if (to.meta.requiresAuth && !authStore.token) {
    next('/login')
  } else {
    next()
  }
})

// Google Analytics page tracking
router.afterEach((to) => {
  // Track page view with Google Analytics
  if ((import.meta as any).env.VITE_GA_TRACKING_ID) {
    pageview({
      page_title: to.meta.title as string || document.title,
      page_location: window.location.href,
      page_path: to.fullPath
    })
  }
})

export default router
