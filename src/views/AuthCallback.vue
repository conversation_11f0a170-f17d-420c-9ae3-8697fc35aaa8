<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md">
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span class="ml-2">Authenticating...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { redirectToApp } from '../utils/redirect';

const router = useRouter();
const authStore = useAuthStore();

onMounted(async () => {
  try {
    const params = new URLSearchParams(window.location.search);
    console.log('OAuth callback params:', {
      token: params.get('token') ? 'present' : 'missing',
      user: params.get('user') ? 'present' : 'missing'
    });

    await authStore.handleGithubCallback(params);

    // Redirect to app_frontend_agentq (microfrontend)
    console.log('OAuth callback - Redirecting to app');
    redirectToApp(100);
  } catch (error) {
    console.error('Authentication callback error:', error);
    router.push('/login');
  }
});
</script>
