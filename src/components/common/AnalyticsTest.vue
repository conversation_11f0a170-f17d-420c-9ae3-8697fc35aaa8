<template>
  <div class="analytics-test p-4 border rounded-lg bg-gray-50">
    <h3 class="text-lg font-semibold mb-4">Analytics Test Component</h3>
    <p class="text-sm text-gray-600 mb-4">
      This component is for testing Google Analytics integration. 
      Open browser dev tools and check the Network tab for analytics requests.
    </p>
    
    <div class="grid grid-cols-2 gap-4">
      <button 
        @click="testPageView"
        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Test Page View
      </button>
      
      <button 
        @click="testCustomEvent"
        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
      >
        Test Custom Event
      </button>
      
      <button 
        @click="testLogin"
        class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
      >
        Test Login Event
      </button>
      
      <button 
        @click="testError"
        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
      >
        Test Error Event
      </button>
      
      <button 
        @click="testButtonClick"
        class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
      >
        Test Button Click
      </button>
      
      <button 
        @click="testFormSubmission"
        class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600"
      >
        Test Form Submit
      </button>
    </div>
    
    <div class="mt-4 p-3 bg-white rounded border">
      <h4 class="font-medium mb-2">Last Event:</h4>
      <pre class="text-xs text-gray-700">{{ lastEvent }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { pageview } from 'vue-gtag'
import { 
  trackEvent, 
  trackLogin, 
  trackButtonClick, 
  trackFormSubmission, 
  trackError 
} from '../../utils/analytics'

const lastEvent = ref('No events triggered yet')

const testPageView = () => {
  pageview({
    page_title: 'Test Page View',
    page_location: window.location.href,
    page_path: '/test-page-view'
  })
  lastEvent.value = 'Page view tracked: /test-page-view'
}

const testCustomEvent = () => {
  trackEvent('test_custom_event', {
    test_parameter: 'test_value',
    timestamp: new Date().toISOString()
  })
  lastEvent.value = 'Custom event tracked: test_custom_event'
}

const testLogin = () => {
  trackLogin('test_method')
  lastEvent.value = 'Login event tracked: test_method'
}

const testError = () => {
  trackError('This is a test error', 'test_error')
  lastEvent.value = 'Error event tracked: This is a test error'
}

const testButtonClick = () => {
  trackButtonClick('test_button', 'analytics_test_component')
  lastEvent.value = 'Button click tracked: test_button'
}

const testFormSubmission = () => {
  trackFormSubmission('test_form', true)
  lastEvent.value = 'Form submission tracked: test_form (success: true)'
}
</script>

<style scoped>
/* Component-specific styles if needed */
</style>
