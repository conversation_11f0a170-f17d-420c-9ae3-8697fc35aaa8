import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import axios from 'axios';
import { useAuthStore } from './stores/auth';
import { configure } from 'vue-gtag';

// Update the import path to point to the correct file
import './assets/styles/main.scss';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);

// Configure Google Analytics with vue-gtag
configure({
  tagId: (import.meta as any).env.VITE_GA_TRACKING_ID || 'G-JVSJ3Q22KF',
  config: {
    page_title: document.title,
    page_location: window.location.href,
  }
});

// Configure axios interceptors for automatic token refresh
axios.interceptors.request.use(async (config) => {
  // Get the auth store
  const authStore = useAuthStore();
  
  // Check if token needs refreshing before making the request
  if (authStore.isAuthenticated) {
    await authStore.checkTokenExpiry();
    
    // Update the Authorization header with the current token
    config.headers.Authorization = authStore.getAuthHeader;
  }
  
  return config;
}, (error) => {
  return Promise.reject(error);
});

axios.interceptors.response.use((response) => {
  return response;
}, async (error) => {
  const originalRequest = error.config;

  // Skip refresh logic for login and signup endpoints
  const skipRefreshEndpoints = ['/auth/login', '/auth/signup'];
  if (
    error.response?.status === 401 &&
    !originalRequest._retry &&
    !skipRefreshEndpoints.some((endpoint) =>
      originalRequest.url?.includes(endpoint)
    )
  ) {
    originalRequest._retry = true;

    const authStore = useAuthStore();

    // Try to refresh the token
    const refreshed = await authStore.refreshAuthToken();

    if (refreshed) {
      // Update the authorization header
      originalRequest.headers.Authorization = authStore.getAuthHeader;

      // Retry the original request
      return axios(originalRequest);
    } else {
      // If refresh failed, redirect to login
      authStore.logout();
      return Promise.reject(error);
    }
  }

  return Promise.reject(error);
});

app.mount('#app');
