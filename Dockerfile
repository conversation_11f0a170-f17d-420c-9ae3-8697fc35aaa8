# Build stage
FROM node:22-alpine AS builder
WORKDIR /app

# Declare the build argument
ARG VITE_CORE_SERVICE_URL
ARG VITE_APP_URL
ARG VITE_GA_TRACKING_ID

# Set it as an environment variable so npm run build can pick it up
ENV VITE_CORE_SERVICE_URL=${VITE_CORE_SERVICE_URL}
ENV VITE_APP_URL=${VITE_APP_URL}
ENV VITE_GA_TRACKING_ID=${VITE_GA_TRACKING_ID}

COPY package*.json ./
COPY . .
RUN npm ci
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
