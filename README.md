
# AgentQ – Staging Kubernetes Deployment

This repo contains Kubernetes manifests and Helm configuration to manage the **staging environment** of the AgentQ application, including frontend, backend, and NGINX ingress.

---

## 📁 Project Structure

```
staging/
├── backend/       # Backend deployment, service, configmap
├── frontend/      # Frontend deployment, service, configmap, env.js
└── nginx/         # NGINX ingress controller (Helm-managed)
```

---

## 🚀 Deployment Guide

### 🟦 Prerequisites

- `kubectl` configured to the `agentq-staging-01` GKE cluster in `asia-southeast2-a`
- `helm` installed and initialized
- Proper DNS setup pointing to Ingress external IP (Cloudflare proxied is OK)

---

## 🛠️ 1. Backend Deployment

### 🔸 Files:
- `backend/deployment.yaml`
- `backend/service.yaml`
- `backend/configmap.yaml`

### 🔸 Commands:

```bash
# Apply environment config
kubectl apply -f backend/configmap.yaml

# Deploy backend app and service
kubectl apply -f backend/deployment.yaml
kubectl apply -f backend/service.yaml

# Restart deployment (force image pull if same tag)
kubectl patch deployment backend-agentq \
  -p "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"restarted-at\":\"$(date +%s)\"}}}}}"

# Check logs
kubectl logs -l app=backend-agentq

# Health check (if implemented)
curl -i https://staging-backend-core-api.agentq.id/health
```

---

## 🛠️ 2. Frontend Deployment

### 🔸 Files:
- `frontend/deployment.yaml`
- `frontend/service.yaml`
- `frontend/configmap.yaml`
- `frontend/env-js-configmap.yaml`
- `frontend/ingress.yaml`

### 🔸 Commands:

```bash
# Apply static and runtime environment configs
kubectl apply -f frontend/configmap.yaml
kubectl apply -f frontend/env-js-configmap.yaml

# Deploy frontend and expose via service + ingress
kubectl apply -f frontend/deployment.yaml
kubectl apply -f frontend/service.yaml
kubectl apply -f frontend/ingress.yaml

# Restart deployment (force pull)
kubectl patch deployment frontend-agentq \
  -p "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"restarted-at\":\"$(date +%s)\"}}}}}"

# Verify
curl -i https://staging.agentq.id
curl https://staging.agentq.id/env.js
```

---

## 🧩 3. NGINX Ingress Controller (via Helm)

The ingress controller is installed using the official Helm chart.

### 🔸 Installation (first time):

```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update

helm install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx --create-namespace \
  --set controller.replicaCount=1 \
  --set controller.nodeSelector."kubernetes\.io/os"=linux \
  --set controller.admissionWebhooks.patch.nodeSelector."kubernetes\.io/os"=linux \
  --set defaultBackend.nodeSelector."kubernetes\.io/os"=linux
```

### 🔸 Upgrade (existing install):

```bash
helm upgrade ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx
```

### 🔸 Verify Ingress:

```bash
kubectl get ingress -A
kubectl describe ingress frontend-agentq-ingress
```

---

## 🧪 Troubleshooting

- `curl -i` returns `404` → likely wrong path or backend route missing.
- Blank screen on frontend → check if `env.js` is mounted and `window.__ENV__` is used in the app.
- To re-pull an image with the same tag, use the patch trick shown above.

---

## 📎 Notes

- TLS is terminated by **Cloudflare**, using **Origin CA** certs.
- Backend connects to **Cloud SQL** and **MemoryStore Redis** via private IPs.
- If frontend app still uses `import.meta.env`, you must rebuild the app or modify it to read from `window.__ENV__`.

---

Feel free to edit this README as needed for your team SOPs or CI/CD automation.
