apiVersion: apps/v1
kind: Deployment
metadata:
  name: devicefarm-agentq
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: devicefarm-agentq
  template:
    metadata:
      labels:
        app: devicefarm-agentq
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
        - name: appium
          image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/device-farm-hub:1.0.1
          imagePullPolicy: Always
          # === PERUBAHAN DIMULAI DI SINI ===
          # Menimpa ENTRYPOINT dari Dockerfile untuk menjalankan perintah yang lebih sederhana.
          command: ["appium"]
          args:
            - "server"
            - "--use-plugins=device-farm"
            - "--plugin-device-farm-enable-authentication"
            - "--log-level=debug" # Menambahkan log debug untuk informasi lebih detail
          # === PERUBAHAN BERAKHIR DI SINI ===
          ports:
            - containerPort: 4723
          env:
            - name: APPIUM_HOME
              value: /data/appium
            - name: DEVICE_FARM_CACHE_DIR
              value: /data/appium/.cache/appium-device-farm
            - name: HOME
              value: /data/appium/home
          envFrom:
            - secretRef:
                name: appium-admin
          volumeMounts:
            - name: appium-data
              mountPath: /data/appium
          readinessProbe:
            httpGet:
              path: /status
              port: 4723
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
          livenessProbe:
            httpGet:
              path: /status
              port: 4723
            initialDelaySeconds: 30
            periodSeconds: 20
            timeoutSeconds: 5
      volumes:
        - name: appium-data
          persistentVolumeClaim:
            claimName: appium-pvc