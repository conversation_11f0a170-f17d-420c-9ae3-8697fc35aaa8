apiVersion: apps/v1
kind: Deployment
metadata:
  name: appium-node-android
  namespace: default     # konsistenkan namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: appium-node-android
  template:
    metadata:
      labels:
        app: appium-node-android
    spec:
      containers:
        - name: appium-node-android
          image: budtmo/docker-android:emulator_14.0
          env:
            - name: APPIUM
              value: "true"
            - name: DEVICE_NAME
              value: "Android_14_Emulator"
            - name: ADB_SERVER_PORT
              value: "5037"
          ports:
            - containerPort: 6080
            - containerPort: 5555   # ADB over TCP
          lifecycle:
            postStart:
              exec:
                command:
                  - /bin/sh
                  - -lc
                  - |
                    adb wait-for-device || true
                    adb devices || true
                    adb tcpip 5555 || true