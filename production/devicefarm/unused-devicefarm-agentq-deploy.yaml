apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: devicefarm-agentq
  namespace: default
spec:
  serviceName: devicefarm-agentq
  replicas: 1
  selector:
    matchLabels:
      app: devicefarm-agentq
  template:
    metadata:
      labels:
        app: devicefarm-agentq
    spec:
      initContainers:
      - name: init-permissions
        image: busybox
        command: ["sh", "-c", "chown -R 0:0 /root/.cache/appium-device-farm"]
        volumeMounts:
        - name: device-farm-storage
          mountPath: /root
        securityContext:
          runAsUser: 0
      containers:
      - name: devicefarm-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/device-farm-hub:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 4723
        env:
        - name: PORT
          value: "4723"
        volumeMounts:
        - name: device-farm-storage
          mountPath: /root/.cache/appium-device-farm
      volumes: []
  volumeClaimTemplates:
  - metadata:
      name: device-farm-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi