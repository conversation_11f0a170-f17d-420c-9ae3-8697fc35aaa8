apiVersion: v1
kind: Secret
metadata:
  name: backend-agentq-secrets
  namespace: default
type: Opaque
stringData:
  DB_PASSWORD: "C8ygO2p36L5d"
  REDIS_PASSWORD: ""
  JWT_SECRET: "your_jwt_secret_key_change_in_production"
  GITHUB_CLIENT_SECRET: "503b925e80bcb81297bc02619144b895cc8f30a3"
  GOOGLE_CLIENT_SECRET: "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"
  RESEND_API_KEY: "re_LjAfU3GD_FWKthbMiSaHKmR1jXXZFqZJk"
  MIDTRANS_CLIENT_KEY: "Mid-client-yDOohRuMqY_m4QfN"
  MIDTRANS_SERVER_KEY: "Mid-server-vZdpb9fMqgqbSoGx39CU6BDx"
