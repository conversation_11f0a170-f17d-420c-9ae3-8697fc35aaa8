apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-agentq-config
  namespace: default
data:
  NODE_ENV: "development"
  DB_HOST: "***********"
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_DATABASE: "agentq"
  REDIS_URL: "redis://***********:6379"
  REDIS_PASSWORD: ""
  REDIS_DB: "4"
  JWT_SECRET: "your_jwt_secret_key_change_in_production"
  GITHUB_CLIENT_ID: "Ov23liYW8MIhB5HcTj4V"
  GITHUB_CALLBACK_URL: "https://backend-core-api.agentq.id/auth/github/callback"
  GOOGLE_CLIENT_ID: "744412782739-r7tvedrin8mqmtqf4m2lfb54c4730s1o.apps.googleusercontent.com"
  
  GOOGLE_CALLBACK_URL: "https://backend-core-api.agentq.id/auth/google/callback"
  FRONTEND_URL: "https://agentq.id"
  AGENTQ_APP_URL: "https://app.agentq.id"
  AGENTQ_APP_API_URL: "https://backend-app.agentq.id"
  
  RESEND_FROM_EMAIL: "<EMAIL>"
  RESEND_FROM_NAME: "AgentQ"

  # SMTP Configuration
  SMTP_HOST: "smtp.titan.email"
  SMTP_PORT: "587"
  SMTP_SECURE: "true"
  SMTP_USER: "<EMAIL>"
  SMTP_PASS: "Noviantonugroho4@"
  SMTP_FROM: "<EMAIL>"

