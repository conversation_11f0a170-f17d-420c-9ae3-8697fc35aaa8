apiVersion: v1
kind: ConfigMap
metadata:
  name: websocket-ai-single-test-config
  namespace: default
data:
  BACKEND_URL: "https://backend-app.agentq.id"
  CORE_SERVICE_URL: "https://backend-core-api.agentq.id"
  VITE_BACKEND_URL: "https://backend-app.agentq.id"
  AGENTQ_API_URL: "https://backend-app.agentq.id"
  VITE_AI_SERVICE_URL: "https://ai-service.agentq.id"
  PORT: "3021"
  LLM_PROVIDER: "GEMINI"
  GEMINI_MODEL: "gemini-2.0-flash"
  OPENAI_MODEL: "gpt-4o-mini"
  GCP_PROJECT_ID: "agentq-464900"
  GCP_CLIENT_EMAIL: "<EMAIL>"
  GCP_BUCKET_NAME: "prod-agentq"
  ENABLE_CLOUD_STORAGE: "true"
  AGENTQ_AUTH_EMAIL: "<EMAIL>"
  AGENTQ_PROJECT_ID: "5861f260-f05f-4649-a421-2ac264a0b1d4"
  AGENTQ_SERVICE_URL: "wss://websocket-ai-single-test.agentq.id"
  NODE_ENV: "production"
  REDIS_HOST: "***********"
  REDIS_PORT: "6379"
  REDIS_DB: "6"
