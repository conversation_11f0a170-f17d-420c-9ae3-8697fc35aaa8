# loki-values.yaml
deploymentMode: SingleBinary   # <-- run one pod only

loki:
  auth_enabled: false
  commonConfig:
    replication_factor: 1

  # --- GCS storage ---
  storage:
    type: gcs
    bucketNames:
      chunks: agentq
      ruler:  agentq
      admin:  agentq
    gcs:
      chunks_prefix: loki-logs/chunks
      rules_prefix:  loki-logs/rules
      service_account: null   # Workload Identity

  # --- REQUIRED schema for a fresh install ---
  schemaConfig:
    configs:
      - from: "2024-01-01"      # any past date
        store: tsdb
        object_store: gcs
        schema: v13
        index:
          prefix: loki_index_
          period: 24h

singleBinary:
  replicas: 1
  persistence:
    size: 10Gi

serviceAccount:
  create: true
  name: loki
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>