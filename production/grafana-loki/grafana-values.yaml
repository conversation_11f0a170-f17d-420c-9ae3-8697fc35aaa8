# grafana-values.yaml  (delta only)
service:
  type: ClusterIP          # <- NGINX will talk to this

ingress:
  enabled: true
  ingressClassName: nginx  # <- use your NGINX class
  annotations:
    # cert-manager or any other NGINX annotations you like
    #cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
  hosts:
    - monitor.agentq.id
  tls:
    - secretName: grafana-tls
      hosts:
        - monitor.agentq.id