# 1. (once) add <PERSON><PERSON> repos
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update

# 2. (once) create namespace
kubectl create namespace loki

# 3. (once) install cert-manager ClusterIssuer
kubectl apply -f grafana-tls.yaml

# 4. install / upgrade Loki
helm upgrade --install loki grafana/loki \
  --namespace loki \
  --values loki-values.yaml

# 5. install / upgrade Grafana
helm upgrade --install grafana grafana/grafana \
  --namespace loki \
  --values grafana-values.yaml

# 6. install Promtail (log shipping DaemonSet)
helm upgrade --install promtail grafana/promtail \
  --namespace loki \
  --set "loki.serviceName=loki"

# 7. point DNS
# Add an A-record: monitor.agentq.id → <EXTERNAL-IP of your NGINX Ingress Controller>