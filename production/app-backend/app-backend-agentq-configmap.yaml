apiVersion: v1
kind: ConfigMap
metadata:
  name: app-backend-agentq-config
  namespace: default
data:
  NODE_ENV: "development"
  DB_HOST: "***********"
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_DATABASE: "agentq_enterprise"
  ENABLE_CLOUD_STORAGE: "true"
  GCP_PROJECT_ID: "agentq-464900"
  GCP_CLIENT_EMAIL: "<EMAIL>"
  GCP_BUCKET_NAME: "prod-agentq"
  DEVICE_FARM_URL: "https://device-farm.agentq.id"
  DEVICE_FARM_USERNAME: "agentq"
