apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-backend-agentq-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://app.agentq.id"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    nginx.ingress.kubernetes.io/proxy-body-size: "1g"
spec:
  tls:
    - hosts:
        - backend-app.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: backend-app.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: app-backend-agentq-svc
              port:
                number: 80
