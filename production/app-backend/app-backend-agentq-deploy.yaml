apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-backend-agentq
  namespace: default
spec:
  replicas: 5
  selector:
    matchLabels:
      app: app-backend-agentq
  template:
    metadata:
      labels:
        app: app-backend-agentq
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3010"
        prometheus.io/path: "/metrics"    
    spec:
      containers:
      - name: app-backend-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_backend_agentq:3.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3010
        env:
        - name: PORT
          value: "3010"
        envFrom:
        - configMapRef:
            name: app-backend-agentq-config
        - secretRef:
            name: app-backend-agentq-secret
