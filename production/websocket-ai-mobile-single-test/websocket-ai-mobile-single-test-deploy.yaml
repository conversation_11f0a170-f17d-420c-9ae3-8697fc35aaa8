apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-ai-mobile-single-test
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: websocket-ai-mobile-single-test
  template:
    metadata:
      labels:
        app: websocket-ai-mobile-single-test
    spec:
      containers:
        - name: websocket-ai-mobile-single-test
          image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_mobile_single_test:1.0.0
          imagePullPolicy: Always
          ports:
            - containerPort: 3025
          envFrom:
            - configMapRef:
                name: websocket-ai-mobile-single-test-config
            - secretRef:
                name: websocket-ai-mobile-single-test-secret