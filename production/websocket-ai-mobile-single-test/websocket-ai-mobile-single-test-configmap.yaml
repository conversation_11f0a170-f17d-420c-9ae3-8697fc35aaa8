apiVersion: v1
kind: ConfigMap
metadata:
  name: websocket-ai-mobile-single-test-config
  namespace: default
data:
  # Backend / Core mengikuti configmap existing (bukan localhost)
  BACKEND_URL: "https://backend-app.agentq.id"
  CORE_SERVICE_URL: "https://backend-core-api.agentq.id"

  # Frontend envs (bukan localhost)
  VITE_BACKEND_URL: "https://backend-app.agentq.id"
  AGENTQ_API_URL: "https://backend-app.agentq.id"

  # AI service (bukan localhost)
  VITE_AI_SERVICE_URL: "https://ai-service.agentq.id"

  # WebSocket service port (sesuai app baru)
  PORT: "3025"

  # LLM
  LLM_PROVIDER: "GEMINI"
  GEMINI_MODEL: "gemini-2.0-flash"
  OPENAI_MODEL: "gpt-4o-mini"

  # Google Cloud Storage
  GCP_PROJECT_ID: "agentq-464900"
  GCP_CLIENT_EMAIL: "<EMAIL>"
  GCP_BUCKET_NAME: "prod-agentq"
  ENABLE_CLOUD_STORAGE: "true"

  # App/Auth & Project
  AGENTQ_AUTH_EMAIL: "<EMAIL>"
  AGENTQ_PROJECT_ID: "5861f260-f05f-4649-a421-2ac264a0b1d4"

  # WS URL diarahkan ke host mobile baru (wss)
  AGENTQ_SERVICE_URL: "wss://websocket-ai-mobile-single-test.agentq.id"

  # Env
  NODE_ENV: "production"

  # Redis (bukan localhost)
  REDIS_HOST: "***********"
  REDIS_PORT: "6379"
  REDIS_DB: "11"

  DEVICE_FARM_ACCESS_KEY: "agentq_OLYcaB8DCoHyn"
  DEVICE_FARM_TOKEN: "3e197bae-1c02-45f2-b94c-44ed171aa72c"
  DEVICE_FARM_HOSTNAME: "device-farm.agentq.id"
  DEVICE_FARM_PORT: "443"
  DEVICE_FARM_PATH: "/wd/hub"
  

