apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-ai-mobile-single-test-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "1g"
spec:
  tls:
    - hosts:
        - websocket-ai-mobile-single-test.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: websocket-ai-mobile-single-test.agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: websocket-ai-mobile-single-test-svc
                port:
                  number: 80