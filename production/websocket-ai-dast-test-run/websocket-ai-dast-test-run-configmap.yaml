apiVersion: v1
kind: ConfigMap
metadata:
  name: websocket-ai-dast-test-run-config
  namespace: default
data:
  BACKEND_URL:               "https://backend-app.agentq.id"
  AGENTQ_API_URL:            "https://backend-app.agentq.id"
  CORE_SERVICE_URL:          "https://backend-core-api.agentq.id"
  VITE_BACKEND_URL:       "https://backend-app.agentq.id"

  PORT:                      "3024"
  USE_KUBERNETES:            "true"
  K8S_NAMESPACE:             "default"
  ZAP_IMAGE:                 "asia-southeast2-docker.pkg.dev/agentq-464900/agentq/zaproxy:stable"
  ZAP_API_KEY:               "AgentqSuperAI"

  LLM_PROVIDER:              "GEMINI"
  GEMINI_MODEL:              "gemini-2.0-flash"
  OPENAI_MODEL:              "gpt-4o-mini"

  GCP_PROJECT_ID:            "agentq-464900"
  GCP_CLIENT_EMAIL:          "<EMAIL>"
  GCP_BUCKET_NAME:           "prod-agentq"
  ENABLE_CLOUD_STORAGE:      "true"

  AGENTQ_AUTH_EMAIL:         "<EMAIL>"
  AGENTQ_PROJECT_ID:         "5861f260-f05f-4649-a421-2ac264a0b1d4"
  AGENTQ_SERVICE_URL:     "ws://websocket-ai-dast-test-run.agentq.id"
  AGENTQ_ENV:             "production"

  NODE_ENV:                  "production"
  REDIS_HOST:               "***********"          
  REDIS_PORT:               "6379"
  REDIS_PASSWORD:           ""
  REDIS_DB:                 "9"