apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-ai-dast-test-run
  namespace: default
spec:
  replicas: 4
  selector:
    matchLabels:
      app: websocket-ai-dast-test-run
  template:
    metadata:
      labels:
        app: websocket-ai-dast-test-run
    spec:
      containers:
      - name: websocket-ai-dast-test-run
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_dast_test_run:1.2.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3024
        envFrom:
        - configMapRef:
            name: websocket-ai-dast-test-run-config
        - secretRef:
            name: websocket-ai-dast-test-run-secret
