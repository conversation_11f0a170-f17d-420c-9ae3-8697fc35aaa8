apiVersion: v1
kind: ConfigMap
metadata:
  name: app-frontend-agentq-config
  namespace: default
data:
  VITE_CORE_SERVICE_URL: "https://backend-core-api.agentq.id"
  VITE_BACKEND_URL:       "https://backend-app.agentq.id"
  VITE_AI_SERVICE_URL:    "https://backend-ai-api.agentq.id"
  VITE_REQUIREMENT_SERVICE_URL: "https://backend-requirement.agentq.id"  
  VITE_WEBSOCKET_URL:     "wss://websocket-ai-single-test.agentq.id"
  VITE_WEBSOCKET_TESTRUN_URL: "wss://websocket-ai-test.agentq.id"
  VITE_WEBSOCKET_SECURITY_URL: "ws://websocket-ai-dast-single-test.agentq.id"
  VITE_WEBSOCKET_TESTRUN_SECURITY_URL: "ws://websocket-ai-dast-test-run.agentq.id"
  VITE_FRONTEND_URL: "https://agentq.id"