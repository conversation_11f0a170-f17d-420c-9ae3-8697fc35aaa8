apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-frontend-agentq-ingress
  namespace: default
  annotations:
#    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
    - hosts:
        - app.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: app.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: app-frontend-agentq-svc
              port:
                number: 80
