apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-ai-test-run-ingress
  namespace: default
  annotations:
#    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
    - hosts:
        - websocket-ai-test-run.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: websocket-ai-test-run.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: websocket-ai-test-run-svc
              port:
                number: 80
