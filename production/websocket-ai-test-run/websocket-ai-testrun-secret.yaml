apiVersion: v1
kind: Secret
metadata:
  name: websocket-ai-testrun-secret
  namespace: default
type: Opaque
stringData:
  JWT_SECRET:            "your_jwt_secret_key_change_in_production"
  GEMINI_API_KEY:        "AIzaSyDjHPbanv4-BMUPU_BxUayBS1B3N0M_H40"
  OPENAI_API_KEY:        "your-openai-api-key-here"
  GCP_PRIVATE_KEY: "-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCotYvxq1kPOLoC\nnLB8bbhrNKtHn6ilQyDhyJV4p6pcm18+z65Rkws48ddRtnwFfo/h9L54P3k9cRob\nmIwMDW6jOTcg7+rclOoeOzzP+ldgoeJpdu8BSp4wEK7iwNfEGFQQ2biJ0Z02Kt0d\nntjdCBrcw4I991oQ+p9o8THB5s056KBSCa8tayYygOKDyoljsiXHzmnH+ye8cn1Z\nXxH/6yi4p21QROkKIGqoSteF9NNsOB2nSVFg5I6hLaV+Vs0QToI2QVK4oMjecg0Y\nKOQeIxAJw9Ebh/2I7Bk8XH3BoS2rQAvuR/tpZmP817pm9bZNIih4yhMzJRZCp7fA\nbz0UewF1AgMBAAECggEAQ0WVBmoZSpkHjugU9aC4o0Lus1ImKLPSRkW52FEe/OtV\neNHORCfalzvT7HjUDkrPq71wP7mrqgTR8MQMv/eRMNOtOFyCba+RhTmAv7umoNkc\nU4qBtiWfWzIj0jYsMzyyNL/kNkxXoFRKNDf1aHmFYJcy9WuOxetxqiJXAWb7WYXT\nLYD5hWuxCbspymVOGKFKJ8iP/gNMOKFbRi640/mjoPHAwol4SOaHnDsBrKx4JXBD\nF9/4us80L3BbfGW7X94L1mjgMQJvPtpM1DxFFXrxQbvyjLj9SheEOQjjww3UElvo\nFa0WSbhRw5CIcbtJ+eEUlK5HdPzgyHoelf3ncaPHFwKBgQDhwWS9nOdhXwi36ZKh\nn1GpO88tScTGycw26tKgf1Z/q/BZLpFSw1rqWIkFKAq3n3BRHkWrR155a0DG3Ffa\n0s+nfcV2QRlikxrrZJZOKki19cGdMO0YcK5lw03zn6B6C0Qk+8OX1QspQsNwjqr2\nKfD+U9RBAQrpEdY5iyFBxgIuJwKBgQC/T6tYX0CWMl5QXwdTbuaoZOIojBI89Qs+\n7XLy1cdiW/fC2rh95RhXyrLb4xQBAhUo1D0m1EeeA3vre6L4v7Pxaz+N+wrE+hmK\nfIqS/x0K0y4Xjz/7GeOBC9jpf+sXBoQ2XBMilpvDIXc8NfnYqSKm4xO6RUyxaq+E\n4Nx37ckxAwKBgQDe5zoV8kePn+sW1FxAkvNperF8UPsnIHcvvjpL2I9KrjU5iT5a\nDqPFAbgAzGGQnq+imJZHoMdKcbja4RMp776y7P88XNi4uPD6fS3fbeavK0GJeJaR\n7Ch2OBnOs80a0+br8V5VmzhS/CXHDEcHma/nHlRT/riMGl2AIbyrmdvrPwKBgQCS\n7QDPEfhsYaqFVzYtIZML6M64DTtEjX1hisMpNH24UKFriMcACR407nuG3FLUo2DU\nxotgcs+9zzcwlveGytPLqW5aAF85AEr1TMiwNDV9xsozVdTxZ+SNiye/zHokaODJ\nTtVzTu+5YM5N87Y6a0stmF/iQq6v4xuBlaA8iuZlOQKBgHGILt5cHCoPSquIbiM7\n7HEUR1MKdfbdxGjHD9WttGqmHHKD7FNdTxLqHKcs6dKweUyc1I9kTgAM2v5kAEcp\nWtKYf8ZDSa3puIzhP91w4/Wn+FOSnXTwm92gB5czIuHgYN3kF536JkH7xDOeS4PL\n+kZYlX1nG0iE3l25UoEW6vHf\n-----END PRIVATE KEY-----\n"
  AGENTQ_JWT_TOKEN:      ""            # fill if you have one
