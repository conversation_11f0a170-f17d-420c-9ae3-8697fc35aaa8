apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-ai-test-run
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: websocket-ai-test-run
  template:
    metadata:
      labels:
        app: websocket-ai-test-run
    spec:
      containers:
      - name: websocket-ai-test-run
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_test_run:1.2.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3022
        envFrom:
        - configMapRef:
            name: websocket-ai-testrun-config
        - secretRef:
            name: websocket-ai-testrun-secret
