apiVersion: v1
kind: ConfigMap
metadata:
  name: websocket-ai-testrun-config
  namespace: default
data:
  BACKEND_URL:              "https://backend-app.agentq.id"
  VITE_BACKEND_URL:         "https://backend-app.agentq.id"
  AGENTQ_API_URL:           "https://backend-app.agentq.id"
  VITE_AI_SERVICE_URL:      "https://ai-service.agentq.id"
  PORT:                     "3022"
  LLM_PROVIDER:             "GEMINI"
  GEMINI_MODEL:             "gemini-2.0-flash"
  OPENAI_MODEL:             "gpt-4o-mini"
  CORE_SERVICE_URL:         "https://backend-core-api.agentq.id"
  GCP_PROJECT_ID:           "agentq-464900"
  GCP_CLIENT_EMAIL:         "<EMAIL>"
  GCP_BUCKET_NAME:          "prod-agentq"
  ENABLE_CLOUD_STORAGE:     "true"
  NODE_ENV:                 "production"
  VITE_WEBSOCKET_URL:       "wss://websocket-ai-test-run.agentq.id"
  REDIS_HOST:               "***********"          
  REDIS_PORT:               "6379"
  REDIS_PASSWORD:           ""
  REDIS_DB:                 "7"
