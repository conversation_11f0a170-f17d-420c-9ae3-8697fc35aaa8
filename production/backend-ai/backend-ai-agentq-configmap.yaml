apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-ai-agentq-config
  namespace: default
data:
  DB_HOST: "***********"          
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_DATABASE: "ai_service"
  GOOGLE_CLOUD_PROJECT_ID: "agentq-464900"
  GOOGLE_CLOUD_CLIENT_EMAIL: "<EMAIL>"
  GOOGLE_CLOUD_BUCKET: "prod-agentq"
  LLM: "GEMINI"
  REDIS_HOST: "***********"
  REDIS_PORT: "6379"
  REDIS_DB: "5"
  NODE_ENV: "production"