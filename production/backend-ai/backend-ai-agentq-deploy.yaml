apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-ai-agentq
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend-ai-agentq
  template:
    metadata:
      labels:
        app: backend-ai-agentq
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"  
    spec:
      containers:
      - name: backend-ai-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_ai_agentq:1.2.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
        env:
        - name: PORT
          value: "3001"
        envFrom:
        - configMapRef:
            name: backend-ai-agentq-config
        - secretRef:
            name: backend-ai-agentq-secret
