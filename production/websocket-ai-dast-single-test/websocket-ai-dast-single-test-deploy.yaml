apiVersion: apps/v1
kind: Deployment
metadata:
  name: ws-ai-dast-single-test
  namespace: default
spec:
  replicas: 4
  selector:
    matchLabels:
      app: ws-ai-dast-single-test
  template:
    metadata:
      labels:
        app: ws-ai-dast-single-test
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3023"
        prometheus.io/path: "/metrics"          
    spec:
      containers:
      - name: ws-ai-dast-single-test
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_dast_single_test:1.2.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3023
        envFrom:
        - configMapRef:
            name: ws-ai-dast-single-test-config
        - secretRef:
            name: ws-ai-dast-single-test-secret
