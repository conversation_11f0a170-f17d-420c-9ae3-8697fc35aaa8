apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      scrape_timeout: 10s
      evaluation_interval: 30s

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
        - targets: ['localhost:9090']

      # Scrape POD ber-anotasi prometheus.io/scrape="true"
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - action: keep
          source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          regex: 'true'
        - action: replace
          source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          target_label: __metrics_path__
          regex: '(.+)'
        # __address__ = <pod_ip>:<port_anno>
        - action: replace
          source_labels: [__meta_kubernetes_pod_ip, __meta_kubernetes_pod_annotation_prometheus_io_port]
          target_label: __address__
          regex: '(.+);(.+)'
          replacement: '$1:$2'
        - action: replace
          source_labels: [__meta_kubernetes_namespace]
          target_label: namespace
        - action: replace
          source_labels: [__meta_kubernetes_pod_label_app]
          target_label: app
        - action: replace
          source_labels: [__meta_kubernetes_pod_name]
          target_label: pod

      # (Opsional) Scrape SERVICE ber-anotasi prometheus.io/scrape="true"
      - job_name: 'kubernetes-services'
        kubernetes_sd_configs:
        - role: service
        relabel_configs:
        - action: keep
          source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          regex: 'true'
        - action: replace
          source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
          target_label: __metrics_path__
          regex: '(.+)'
        # __address__ = <svc_host>:<port_anno>
        - action: replace
          source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
          target_label: __address__
          regex: '([^:]+)(?::[0-9]+)?;([0-9]+)'
          replacement: '$1:$2'
        - action: replace
          source_labels: [__meta_kubernetes_namespace]
          target_label: namespace
        - action: replace
          source_labels: [__meta_kubernetes_service_label_app]
          target_label: app