apiVersion: apps/v1
kind: Deployment
metadata:
  name: metabase
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metabase
  template:
    metadata:
      labels:
        app: metabase
    spec:
      securityContext:
        fsGroup: 2000
      containers:
      - name: metabase
        image: metabase/metabase:v0.56.7
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: http
        env:
        # ===== QUICK START (H2) dengan PVC =====
        - name: MB_DB_FILE
          value: /metabase-data/metabase.db

        # ===== PRODUKSI (PostgreSQL) — UNCOMMENT & lengkapi =====
        # - name: MB_DB_TYPE
        #   value: postgres
        # - name: MB_DB_DBNAME
        #   valueFrom: { secretKeyRef: { name: metabase-db, key: database } }
        # - name: MB_DB_PORT
        #   value: "5432"
        # - name: MB_DB_USER
        #   valueFrom: { secretKeyRef: { name: metabase-db, key: username } }
        # - name: MB_DB_PASS
        #   valueFrom: { secretKeyRef: { name: metabase-db, key: password } }
        # - name: MB_DB_HOST
        #   valueFrom: { secretKeyRef: { name: metabase-db, key: host } }

        # ===== Site URL (sesuaikan domain) =====
        - name: MB_SITE_URL
          value: https://metabase.agentq.id

        # ===== Tuning opsional =====
        - name: JAVA_TOOL_OPTIONS
          value: "-Xms512m -Xmx2048m"  # sesuaikan memori
        - name: MB_JETTY_MAXTHREADS
          value: "200"

        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 6
        volumeMounts:
        - name: data
          mountPath: /metabase-data

      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: metabase-pvc