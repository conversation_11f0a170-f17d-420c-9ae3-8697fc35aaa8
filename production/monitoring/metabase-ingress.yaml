apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: metabase-ingress
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"  # samakan gaya grafana kamu
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - metabase.agentq.id           # TODO: ganti domain
    secretName: monitoring-tls     # gunakan secret yg sama jika wildcard/tls sama
  rules:
  - host: metabase.agentq.id
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: metabase
            port:
              number: 80