apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name agentq.id www.agentq.id;

        # /signin -> /login (tetap)
        location = /signin { return 301 /login; }

        # ✅ Root "/" -> landing page GitHub Pages
        location = / {
            return 302 /agentq;
        }

        # ===== GitHub Pages di /agentq/ =====
        location /agentq/ {
            proxy_pass https://agentq-ai.github.io/agentq/;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";
            sub_filter_once  off;
            sub_filter_types text/html text/css application/javascript;
            sub_filter 'http://agentq.id/assets/' 'https://agentq.id/agentq/assets/';
            proxy_redirect https://agentq-ai.github.io/agentq/ /agentq/;
        }

        # /docs -> /agentq/docs
        location /docs/ {
            rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
            proxy_pass https://agentq-ai.github.io;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";
            proxy_redirect https://agentq-ai.github.io/agentq/ /docs/;
            expires 7d;
            add_header Cache-Control public;
        }

        # /login -> frontend (eks 5173)
        location ^~ /login {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_intercept_errors on;
            error_page 404 = @login_spa_fallback;
        }
        location @login_spa_fallback {
            rewrite ^ / break;
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        # semua selain /login -> app (eks 5174)
        location / {
            proxy_pass http://app-frontend-agentq-svc:80$request_uri;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_intercept_errors on;
            error_page 404 = @app_spa_fallback;
        }
        location @app_spa_fallback {
            rewrite ^ / break;
            proxy_pass http://app-frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        add_header Access-Control-Allow-Origin *;
    }