apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name agentq.id www.agentq.id;

        # Redirect /signin → /login
        location = /signin { return 301 /login; }

        ####################################################################
        # 1) Root  /   —> 301 to /agentq/
        ####################################################################
        location = / {
            return 301 /agentq/;
        }

        ####################################################################
        # 2) GitHub Pages under /agentq/
        ####################################################################
        location /agentq/ {
            proxy_pass https://agentq-ai.github.io/agentq/;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";

            # ***DO NOT alter HTML; only mixed-content asset URLs***
            sub_filter_once  off;
            sub_filter_types text/html text/css application/javascript;
            sub_filter 'http://agentq.id/assets/' 'https://agentq.id/agentq/assets/';

            proxy_redirect https://agentq-ai.github.io/agentq/ /agentq/;
        }

        ####################################################################
        # 3) /docs/...  → /agentq/docs/...
        ####################################################################
        location /docs/ {
            rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
            proxy_pass https://agentq-ai.github.io;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";
            proxy_redirect https://agentq-ai.github.io/agentq/ /docs/;
            expires 7d;
            add_header Cache-Control public;
        }

        ####################################################################
        # 4) Vue app static assets
        ####################################################################
        location /assets/ {
            proxy_pass http://frontend-agentq-svc:80/assets/;
            proxy_set_header Host $host;
            expires 30d;
            add_header Cache-Control public;
        }

        ####################################################################
        # 5) Catch-all → Vue SPA (404 → index.html)
        ####################################################################
        location / {
            proxy_pass http://frontend-agentq-svc:80$request_uri;
            proxy_set_header Host $host;
            proxy_intercept_errors on;
            error_page 404 = @spa_fallback;
        }

        location @spa_fallback {
            rewrite ^ / break;
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host $host;
        }

        # Optional CORS
        add_header Access-Control-Allow-Origin *;
    }