apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
      listen 80;
      server_name agentq.id www.agentq.id;

      # --- Redirect sederhana
      location = /signin { return 301 /login; }
      location = /       { return 301 /agentq/; }

      # --- Landing GitHub Pages
      location /agentq/ {
        proxy_pass https://agentq-ai.github.io/agentq/;
        proxy_set_header Host               agentq-ai.github.io;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        proxy_set_header Accept-Encoding    "";
        sub_filter_once  off;
        sub_filter_types text/html text/css application/javascript;
        sub_filter 'http://agentq.id/assets/' 'https://agentq.id/agentq/assets/';
        proxy_redirect https://agentq-ai.github.io/agentq/ /agentq/;
      }

      # --- /docs -> /agentq/docs
      location /docs/ {
        rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
        proxy_pass https://agentq-ai.github.io;
        proxy_set_header Host               agentq-ai.github.io;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;
        proxy_set_header Accept-Encoding    "";
        proxy_redirect https://agentq-ai.github.io/agentq/ /docs/;
        expires 7d;
        add_header Cache-Control public;
      }

      # ===== LOGIN (SPA) → frontend-agentq =====
      location ^~ /login {
        proxy_pass http://frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        proxy_intercept_errors on;
        error_page 404 405 500 502 503 504 =200 @login_spa_index;

        etag off; if_modified_since off;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
      }

      # ===== AUTH CALLBACK (SPA) → frontend-agentq =====
      location ^~ /auth/ {
        proxy_pass http://frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        proxy_intercept_errors on;
        error_page 404 405 500 502 503 504 =200 @login_spa_index;

        proxy_connect_timeout 5s;
        proxy_send_timeout    30s;
        proxy_read_timeout    30s;
        proxy_next_upstream error timeout invalid_header http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
      }

      # ===== Handler SPA INDEX (login) — ambil "/" dari frontend =====
      location @login_spa_index {
        proxy_pass http://frontend-agentq-svc:80/;  # trailing "/" agar ambil index.html
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        etag off; if_modified_since off;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
      }

      # ===== ASET (/assets/*): SEKARANG coba APP dulu, 404 → fallback ke FRONTEND =====
      location ^~ /assets/ {
        # Prioritas ke app-frontend (dashboard)
        proxy_pass http://app-frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        proxy_intercept_errors on;
        # Jika tidak ada di APP, lempar ke FRONTEND
        error_page 404 405 500 502 503 504 = @assets_frontend;

        expires 30d;
        add_header Cache-Control "public, immutable";
      }

      # Fallback assets ke FRONTEND (login)
      location @assets_frontend {
        proxy_pass http://frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;
        expires 30d;
        add_header Cache-Control "public, immutable";
      }

      # ===== APP (SPA) → app-frontend-agentq =====
      location / {
        proxy_pass http://app-frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;

        proxy_intercept_errors on;
        error_page 404 = @app_spa_fallback;
      }

      location @app_spa_fallback {
        rewrite ^ / break;
        proxy_pass http://app-frontend-agentq-svc:80;
        proxy_set_header Host               $host;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;
      }

      add_header Access-Control-Allow-Origin *;
    }