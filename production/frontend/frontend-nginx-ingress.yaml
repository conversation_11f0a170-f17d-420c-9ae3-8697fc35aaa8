apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-nginx-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
#    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - agentq.id
        - www.agentq.id
        - app.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-nginx-svc
                port:
                  number: 80
    - host: www.agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-nginx-svc
                port:
                  number: 80
    - host: app.agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-nginx-svc
                port:
                  number: 80