apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-nginx-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect:       "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - agentq.id
        - www.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-nginx-svc
                port:
                  number: 80
    - host: www.agentq.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-nginx-svc
                port:
                  number: 80
    - host: agentq.id
      http:
        paths:
          - path: /login
            pathType: Prefix
            backend:
              service:
                name: frontend-agentq-svc
                port:
                  number: 80              
