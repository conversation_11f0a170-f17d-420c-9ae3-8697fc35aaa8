apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-ai-automation-test-api-ingress
  namespace: default
  annotations:
#    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
    - hosts:
        - websocket-ai-automation-test-api.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: websocket-ai-automation-test-api.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: ws-ai-automation-test-api-svc
              port:
                number: 80
