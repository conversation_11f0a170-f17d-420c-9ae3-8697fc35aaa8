apiVersion: apps/v1
kind: Deployment
metadata:
  name: ws-ai-automation-test-api
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ws-ai-automation-test-api
  template:
    metadata:
      labels:
        app: ws-ai-automation-test-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3008"
        prometheus.io/path: "/metrics"          
    spec:
      containers:
      - name: ws-ai-automation-test-api
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_automation_test_agentq:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3008
        envFrom:
        - configMapRef:
            name: ws-ai-automation-test-api-config
        - secretRef:
            name: ws-ai-automation-test-api-secret
