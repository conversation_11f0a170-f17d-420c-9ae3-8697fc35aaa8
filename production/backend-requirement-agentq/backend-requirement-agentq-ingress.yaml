apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-requirement-agentq-ingress
  namespace: default
  annotations:
#    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "1g"
spec:
  tls:
    - hosts:
        - backend-requirement-api.agentq.id
      secretName: frontend-agentq-tls
  rules:
    - host: backend-requirement-api.agentq.id
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: backend-requirement-agentq-svc
              port:
                number: 80
