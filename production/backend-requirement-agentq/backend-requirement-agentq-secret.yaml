apiVersion: v1
kind: Secret
metadata:
  name: backend-requirement-agentq-secret
  namespace: default
type: Opaque
stringData:
  DB_PASSWORD: "C8ygO2p36L5d"
  JWT_SECRET: "your_jwt_secret_key_change_in_production"
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  OPENAI_API_KEY: "***************************************************"
  GEMINI_API_KEY: "AIzaSyC-4i1YvPTCexBUpw7r8PXo6NWRcIBjwoI"
