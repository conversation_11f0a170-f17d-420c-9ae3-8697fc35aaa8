apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-requirement-agentq
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend-requirement-agentq
  template:
    metadata:
      labels:
        app: backend-requirement-agentq
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3002"
        prometheus.io/path: "/metrics"          
    spec:
      containers:
      - name: backend-requirement-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_requirement_agentq:1.0.1
        imagePullPolicy: Always
        ports:
        - containerPort: 3002
        env:
        - name: PORT
          value: "3002"
        envFrom:
        - configMapRef:
            name: backend-requirement-agentq-config
        - secretRef:
            name: backend-requirement-agentq-secret
