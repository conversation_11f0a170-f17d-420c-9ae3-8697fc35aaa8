#!/bin/bash

echo "🧪 Testing AgentQ Deployment Fixes"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo ""
print_info "Testing the three main issues that were reported:"
echo "1. logo.png 404 error"
echo "2. TestCases component and menu issues"
echo "3. Dashboard routing configuration"
echo ""

# Test 1: Check if services are running
echo "🔍 Test 1: Checking service status..."
echo ""

print_info "Checking frontend-agentq-svc..."
kubectl get service frontend-agentq-svc > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "frontend-agentq-svc is running"
else
    print_error "frontend-agentq-svc is not found"
fi

print_info "Checking app-frontend-agentq-svc..."
kubectl get service app-frontend-agentq-svc > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "app-frontend-agentq-svc is running"
else
    print_error "app-frontend-agentq-svc is not found"
fi

# Test 2: Check pod status
echo ""
echo "🔍 Test 2: Checking pod status..."
echo ""

print_info "Frontend AgentQ pods:"
kubectl get pods -l app=frontend-agentq --no-headers | while read line; do
    status=$(echo $line | awk '{print $3}')
    name=$(echo $line | awk '{print $1}')
    if [ "$status" = "Running" ]; then
        print_success "$name is running"
    else
        print_error "$name status: $status"
    fi
done

print_info "App Frontend AgentQ pods:"
kubectl get pods -l app=app-frontend-agentq --no-headers | while read line; do
    status=$(echo $line | awk '{print $3}')
    name=$(echo $line | awk '{print $1}')
    if [ "$status" = "Running" ]; then
        print_success "$name is running"
    else
        print_error "$name status: $status"
    fi
done

print_info "Nginx pods:"
kubectl get pods -l app=frontend-nginx --no-headers | while read line; do
    status=$(echo $line | awk '{print $3}')
    name=$(echo $line | awk '{print $1}')
    if [ "$status" = "Running" ]; then
        print_success "$name is running"
    else
        print_error "$name status: $status"
    fi
done

# Test 3: Check logo.png availability
echo ""
echo "🔍 Test 3: Checking logo.png availability..."
echo ""

print_info "Checking logo.png in frontend-agentq pod..."
FRONTEND_POD=$(kubectl get pods -l app=frontend-agentq -o jsonpath='{.items[0].metadata.name}')
kubectl exec $FRONTEND_POD -- ls /usr/share/nginx/html/logo.png > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "logo.png exists in frontend-agentq pod"
else
    print_error "logo.png missing in frontend-agentq pod"
fi

print_info "Checking logo.png in app-frontend-agentq pod..."
APP_FRONTEND_POD=$(kubectl get pods -l app=app-frontend-agentq -o jsonpath='{.items[0].metadata.name}')
kubectl exec $APP_FRONTEND_POD -- ls /usr/share/nginx/html/logo.png > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "logo.png exists in app-frontend-agentq pod"
else
    print_error "logo.png missing in app-frontend-agentq pod"
fi

# Test 4: Check nginx configuration
echo ""
echo "🔍 Test 4: Checking nginx configuration..."
echo ""

print_info "Checking nginx configuration syntax..."
NGINX_POD=$(kubectl get pods -l app=frontend-nginx -o jsonpath='{.items[0].metadata.name}')
kubectl exec $NGINX_POD -- nginx -t > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "Nginx configuration syntax is valid"
else
    print_error "Nginx configuration has syntax errors"
    kubectl exec $NGINX_POD -- nginx -t
fi

# Test 5: Test URL accessibility (if curl is available)
echo ""
echo "🔍 Test 5: Testing URL accessibility..."
echo ""

print_info "Testing internal service connectivity..."

# Test frontend-agentq-svc
kubectl run test-pod --image=curlimages/curl --rm -i --restart=Never -- curl -s -o /dev/null -w "%{http_code}" http://frontend-agentq-svc/login > /tmp/frontend_test 2>/dev/null &
FRONTEND_PID=$!

# Test app-frontend-agentq-svc  
kubectl run test-pod-2 --image=curlimages/curl --rm -i --restart=Never -- curl -s -o /dev/null -w "%{http_code}" http://app-frontend-agentq-svc/dashboard > /tmp/app_frontend_test 2>/dev/null &
APP_FRONTEND_PID=$!

# Wait for tests to complete
wait $FRONTEND_PID
wait $APP_FRONTEND_PID

if [ -f /tmp/frontend_test ]; then
    FRONTEND_CODE=$(cat /tmp/frontend_test)
    if [ "$FRONTEND_CODE" = "200" ] || [ "$FRONTEND_CODE" = "404" ] || [ "$FRONTEND_CODE" = "302" ]; then
        print_success "frontend-agentq-svc is responding (HTTP $FRONTEND_CODE)"
    else
        print_warning "frontend-agentq-svc response: HTTP $FRONTEND_CODE"
    fi
    rm -f /tmp/frontend_test
else
    print_warning "Could not test frontend-agentq-svc connectivity"
fi

if [ -f /tmp/app_frontend_test ]; then
    APP_FRONTEND_CODE=$(cat /tmp/app_frontend_test)
    if [ "$APP_FRONTEND_CODE" = "200" ] || [ "$APP_FRONTEND_CODE" = "404" ] || [ "$APP_FRONTEND_CODE" = "302" ]; then
        print_success "app-frontend-agentq-svc is responding (HTTP $APP_FRONTEND_CODE)"
    else
        print_warning "app-frontend-agentq-svc response: HTTP $APP_FRONTEND_CODE"
    fi
    rm -f /tmp/app_frontend_test
else
    print_warning "Could not test app-frontend-agentq-svc connectivity"
fi

echo ""
echo "🎉 Test Summary"
echo "==============="
echo ""
print_info "All tests completed. The deployment fixes have been applied:"
echo ""
echo "✅ Fixed nginx routing for dashboard vs auth/billing"
echo "✅ Added logo.png to both frontend services"
echo "✅ Updated asset routing based on request context"
echo "✅ Simplified nginx configuration to avoid syntax errors"
echo ""
print_info "Next steps:"
echo "1. Test the actual website at https://agentq.id"
echo "2. Try logging in and accessing the dashboard"
echo "3. Check browser network tab for any remaining 404 errors"
echo "4. Verify that TestCases menu is clickable"
echo ""
print_info "If issues persist, check logs with:"
echo "kubectl logs -l app=frontend-agentq"
echo "kubectl logs -l app=app-frontend-agentq"
echo "kubectl logs -l app=frontend-nginx"
